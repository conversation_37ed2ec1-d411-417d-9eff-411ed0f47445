declare namespace Cypress {
  interface UserInfo {
    body: {
      token: string;
      userId: string;
    };
  }
  interface Chainable {
    LoginToApp(): Chainable;
    LoginLandingPage(): Chainable<void>;
    Graphql<T = unknown>(
      query: string,
      variables?: unknown
    ): Chainable<Cypress.Response<T>>;
    waitMainPageIsLoaded(): Chainable<void>;
    waitFilesTabIsLoaded(): Chainable<void>;
    waitForElement(selector: string, timeout?: number): Chainable<JQuery<HTMLElement>>;
    waitForElementToDisappear(selector: string, timeout?: number): Chainable<JQuery<HTMLElement>>;
    getDataIdCy({
      idAlias,
      options,
    }: {
      idAlias: string;
      options?: Partial<
        Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow
      >;
    }): Cypress.Chainable<JQuery<HTMLElement>>;
    selectFromDropdown(selector: string, value: string): Chainable<JQuery<HTMLElement>>;
    repeat({
      action,
      times,
    }: {
      action: unknown;
      times: number;
    }): Chainable<void>;
    awaitNetworkResponseCode({
      alias,
      code,
      repeat,
    }: {
      alias: string;
      code: number;
      repeat?: number;
    }): Chainable<void>;
    assertTableColumnSorted(columnName: string, sortedBy: string): Chainable<void>;
  }
}
