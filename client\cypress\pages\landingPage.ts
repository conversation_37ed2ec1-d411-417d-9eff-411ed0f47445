import { ButtonTestIds, EventDetailRow, LandingPageTestIds, SearchTestIds, SnackbarTestIds, buttonSelectors, dialogSelectors, eventDetailSelectors } from '../support/helperFunction/eventScreenHelper';

export const landingPage = {
  verifyColumnHeaders: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.COLUMN_0 }).as('column0');
    cy.get('@column0').should('be.visible');
    cy.get('@column0').should('contain.text', 'Event Name');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.COLUMN_1 }).as('column1');
    cy.get('@column1').should('be.visible');
    cy.get('@column1').should('contain.text', 'Event Time');
  },

  verifyAppTitle: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.APPBAR_TITLE }).as('appTitle');
    cy.get('@appTitle').should('be.visible');
    cy.get('@appTitle').should('have.text', 'Track');
  },

  clickTab: (tabName: string) => cy.get(`[data-testid="home-${tabName.toLowerCase()}-tab"]`).click(),

  verifyTabIsActive: (activeTabName: string) => {
    const activeTab = activeTabName.toLowerCase();
    const inactiveTab = activeTab === 'events' ? 'files' : 'events';

    cy.get(`[data-testid="home-${activeTab}-tab"]`).should('have.attr', 'aria-selected', 'true');
    cy.get(`[data-testid="home-${inactiveTab}-tab"]`).should('have.attr', 'aria-selected', 'false');
  },

  clickBreadcrumb: (breadcrumbText: string) => {
    cy.url().as('initialUrl');
    cy.contains('nav[aria-label="breadcrumb"] a', breadcrumbText).click({ force: true });
  },

  verifyPageNotNavigated: () => {
    cy.get('@initialUrl').then((initialUrl) => {
      cy.url().should('eq', initialUrl);
    });
    cy.contains('nav[aria-label="breadcrumb"] a', 'All Events').should('be.visible');
  },

  selectEvent: (eventName: string) => {
    cy.get('[data-testid="pending-event-row-name"]').should('not.exist');
    cy.contains('[data-testid="event-row-name"]', eventName).should('be.visible').click();
  },

  verifyEventDetails: (rows: EventDetailRow[]) => {
    cy.log('Starting event details verification...');

    rows.forEach((row: EventDetailRow) => {
      const field = row.Field;
      const expectedValue = row['Expected Value'];
      const detailConfig = eventDetailSelectors[field];

      if (!detailConfig) {
        throw new Error(`The field "${field}" is not a valid option to check in event details.`);
      }

      const { selector, assertionType } = detailConfig;

      cy.log(`Verifying "${field}" with expected value: "${expectedValue}" using selector: "${selector}"`);

      cy.getDataIdCy({ idAlias: selector }).as('eventDetail');
      cy.get('@eventDetail').should('be.visible');
      cy.get('@eventDetail').should(assertionType, expectedValue);
    });
    cy.log('All event details verified successfully.');
  },

  verifyEventTimeFormat: (format: string) => {
    cy.get('[data-testid^="event-row-cell-"][data-testid$="-1"]')
      .first()
      .invoke('text')
      .then((timeText) => {
        cy.log(`Verifying time format for: "${timeText}"`);
        const timeFormatRegex = /^\d{1,2}\/\d{1,2}\/\d{4}, \d{1,2}:\d{2} (AM|PM)$/;
        expect(timeText.trim()).to.match(timeFormatRegex, `Event time "${timeText}" should match format ${format}`);
      });
  },

  clickEventNameEdit: () => {
    cy.getDataIdCy({ idAlias: buttonSelectors[ButtonTestIds.EVENT_NAME_EDIT].selector }).as('editButton');
    cy.get('@editButton').should('be.visible');
    cy.get('@editButton').click();
  },

  changeEventName: (newName: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.HOME_DETAIL_EVENT_NAME_TEXTFIELD }).as('nameField');
    cy.get('@nameField').should('be.visible');
    cy.get('@nameField').clear();
    cy.get('@nameField').type(newName);
    cy.get('@nameField').type('{enter}');
    cy.wrap(newName).as('expectedEventName');
  },

  verifyEventNameChanged: () => {
    cy.get('@expectedEventName').then((expectedName) => {
      cy.getDataIdCy({ idAlias: LandingPageTestIds.HOME_DETAIL_NAME }).as('nameDisplay');
      cy.get('@nameDisplay').should('be.visible');
      cy.get('@nameDisplay').should('have.text', expectedName);
    });
  },

  clickViewEventButton: () => {
    cy.getDataIdCy({ idAlias: buttonSelectors[ButtonTestIds.VIEW_EVENT].selector }).as('viewButton');
    cy.get('@viewButton').should('be.visible');
    cy.get('@viewButton').click();
  },

  verifyNavigationToEventDetails: () => {
    cy.url().should('include', '/event/');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.EVENT_CONTAINER }).should('be.visible');
  },

  clickDeleteEventButton: () => {
    cy.getDataIdCy({ idAlias: buttonSelectors[ButtonTestIds.DELETE_EVENT].selector }).as('deleteButton');
    cy.get('@deleteButton').should('be.visible');
    cy.get('@deleteButton').click();
  },

  enterWrongEventName: (wrongName: string) => {
    cy.getDataIdCy({ idAlias: dialogSelectors.input }).as('dialogInput');
    cy.get('@dialogInput').should('be.visible');
    cy.get('@dialogInput').clear();
    cy.get('@dialogInput').type(wrongName);
  },

  verifyDeleteButtonDisabledAndTextboxError: () => {
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('be.disabled');
    cy.getDataIdCy({ idAlias: dialogSelectors.input }).should('have.attr', 'aria-invalid', 'true');
  },

  clickColumnHeader: (columnName: string) => {
    const columnMap: { [key: string]: string } = {
      'Event Name': LandingPageTestIds.COLUMN_0,
      'Event Time': LandingPageTestIds.COLUMN_1,
    };
    cy.getDataIdCy({ idAlias: columnMap[columnName] }).click();
  },

  verifyColumnSortState: (columnName: string, sortedBy: string) => {
    const columnMap: { [key: string]: string } = {
      'Event Name': LandingPageTestIds.COLUMN_0,
      'Event Time': LandingPageTestIds.COLUMN_1,
    };

    // Convert a-z/z-a to ascending/descending
    const order = sortedBy === 'a-z' ? 'ascending' : 'descending';

    // Wait for any loading to complete after clicking
    cy.waitMainPageIsLoaded();
    cy.get('[data-testid="table"] .table__loading').should('not.exist');

    // Wait a bit more for the sort to complete
    cy.wait(1000);

    // First verify the aria-sort attribute is set correctly
    cy.getDataIdCy({ idAlias: columnMap[columnName] }).should('have.attr', 'aria-sort', order);

    // Then verify the actual data is sorted correctly using the original pattern
    cy.assertTableColumnSorted(columnName, sortedBy);
  },

  enterSearchKeyword: (keyword: string) => {
    cy.getDataIdCy({ idAlias: SearchTestIds.SEARCH_INPUT }).as('searchInput');
    cy.get('@searchInput').should('be.visible');
    cy.get('@searchInput').clear();
    cy.get('@searchInput').type(keyword);
  },

  verifySearchResults: (keyword: string, searchType: 'events' | 'files') => {
    let nameSelector: string;
    if (searchType === 'files') {
      cy.waitFilesTabIsLoaded();
      nameSelector = '[data-testid^="file-row-cell-"][data-testid$="-0"]';
    } else {
      cy.waitMainPageIsLoaded();
      nameSelector = `[data-testid="${LandingPageTestIds.EVENT_ROW_NAME}"]`;
    }
    cy.get(nameSelector).should('have.length.greaterThan', 0);
    cy.get(nameSelector).each(($el) => {
      cy.wrap($el)
        .invoke('text')
        .then((text) => {
          expect(text.toLowerCase()).to.contain(keyword.toLowerCase());
        });
    });
  },

  verifyResultsPerPageLabel: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.RESULTS_PER_PAGE_DROPDOWN }).parent().prev().should('contain.text', 'Results Per Page').should('be.visible');
  },

  changeResultsPerPage: (perPage: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.RESULTS_PER_PAGE_DROPDOWN }).click();
    cy.get('[role="listbox"]').contains(perPage).click();
  },

  verifyResultsPerPageChanged: (perPage: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.RESULTS_PER_PAGE_DROPDOWN }).should('have.text', perPage);
  },

  verifyPaginationInitialState: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_TEXT }).invoke('text').as('initialPaginationText');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_PREVIOUS_BUTTON }).should('not.have.class', 'enabled');
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_NEXT_BUTTON }).should('have.class', 'enabled');
  },

  clickNextPage: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_NEXT_BUTTON }).click();
  },

  verifyNavigatedToNextPage: () => {
    cy.get('@initialPaginationText').then((initialText) => {
      cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_TEXT }).invoke('text').should('not.eq', initialText);
    });
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_PREVIOUS_BUTTON }).should('have.class', 'enabled');
  },

  clickPreviousPage: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_PREVIOUS_BUTTON }).click();
  },

  verifyNavigatedToPreviousPage: () => {
    cy.waitMainPageIsLoaded();
    cy.get('@initialPaginationText').then((initialText) => {
      cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_TEXT }).invoke('text').should('eq', initialText);
    });
    cy.getDataIdCy({ idAlias: LandingPageTestIds.PAGINATION_PREVIOUS_BUTTON }).should('not.have.class', 'enabled');
  },

  // Event creation and deletion helper methods
  createTestEvent: (eventName: string) => {
    cy.log(`--- Creating test event: ${eventName} ---`);

    const currentTime = new Date().toISOString();

    cy.request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/api/v1/event`,
      headers: {
        Authorization: `Bearer ${Cypress.env('token')}`,
        'Content-Type': 'application/json',
      },
      body: {
        name: eventName,
        description: 'Test event for deletion testing',
        eventStartDate: currentTime,
        eventEndDate: currentTime,
      },
    }).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body.event).to.have.property('id');
      cy.wrap(response.body.event.id).as('testEventId');
      cy.wrap(eventName).as('testEventName');
      cy.log(`Created test event with ID: ${response.body.event.id}`);
    });
  },

  verifyDeleteConfirmationDialog: (eventName: string) => {
    cy.getDataIdCy({ idAlias: 'confirm-dialog' }).should('be.visible');
    cy.getDataIdCy({ idAlias: 'confirm-dialog-title' }).should('be.visible').and('contain.text', 'Delete Event');
    cy.getDataIdCy({ idAlias: 'confirm-dialog-description' })
      .should('be.visible')
      .and('contain.text', `You are about to delete ${eventName}`)
      .and('contain.text', 'All associated files, match groups, searches, and exports will be removed')
      .and('contain.text', 'Are you sure you want to delete it?');
    cy.getDataIdCy({ idAlias: dialogSelectors.input }).should('be.visible');
    cy.getDataIdCy({ idAlias: 'confirm-dialog-cancel-action' }).should('be.visible').and('not.be.disabled');
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('be.visible').and('be.disabled');
  },

  enterEventNameForDeletion: (eventName: string) => {
    cy.log(`--- Entering event name for deletion: ${eventName} ---`);
    cy.getDataIdCy({ idAlias: dialogSelectors.input }).as('deleteInput');
    cy.get('@deleteInput').should('be.visible');
    cy.get('@deleteInput').clear();
    cy.get('@deleteInput').type(eventName);
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('not.be.disabled');
  },

  confirmEventDeletion: () => {
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('not.be.disabled').click();
    cy.getDataIdCy({ idAlias: 'confirm-dialog' }).should('not.exist');
  },

  verifyEventDeleted: (eventName: string) => {
    cy.waitMainPageIsLoaded();
    cy.get('[data-testid^="event-row-"]').should('exist');
    cy.get('[data-testid^="event-row-"]').each(($row) => {
      cy.wrap($row).should('not.contain.text', eventName);
    });
  },

  clickUploadFilesButton: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_FILE_BUTTON }).click();
  },

  clickNewEvent: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.NEW_EVENT_BUTTON }).click();
  },

  enterEventName: (eventName: string) => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.NEW_EVENT_INPUT }).as('eventNameInput');
    cy.get('@eventNameInput').should('be.visible');
    cy.get('@eventNameInput').clear();
    cy.get('@eventNameInput').type(eventName);
  },

  clickCreateEvent: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.NEW_EVENT_CONFIRM_BUTTON }).click();
  },

  verifySuccessSnackbar: (message: string) => {
    cy.getDataIdCy({ idAlias: SnackbarTestIds.SNACKBAR_BOX }).should('be.visible');
    cy.getDataIdCy({ idAlias: SnackbarTestIds.SNACKBAR_BOX_SUCCESS }).should('be.visible');
    cy.getDataIdCy({ idAlias: SnackbarTestIds.SNACKBAR_BOX }).find('div').last().should('contain.text', message);
  },

  clickCancelUpload: () => {
    cy.getDataIdCy({ idAlias: LandingPageTestIds.UPLOAD_BUTTON }).prev().click();
  },

  verifyEventInTable: (eventName: string) => {
    cy.waitMainPageIsLoaded();
    cy.getDataIdCy({ idAlias: LandingPageTestIds.EVENT_ROW_NAME }).should('contain.text', eventName);
  },

  verifyDeleteButtonEnabledAndClick: () => {
    cy.getDataIdCy({ idAlias: dialogSelectors.confirmButton }).should('not.be.disabled').click();
  },
};
