Cypress.Commands.add('LoginToApp', () =>
  cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v1/admin/login`,
      form: true,
      body: {
        userName: Cypress.env('username'),
        password: Cypress.env('password'),
      },
    })
    .then((userInfo) => {
      console.log('=====', userInfo.body);
      Cypress.env('token', userInfo.body.token);
      Cypress.env('userId', userInfo.body.userId);
      return userInfo;
    })
);

Cypress.Commands.add('LoginLandingPage', () => {
  const url = Cypress.env('visitUrl');
  cy.LoginToApp();
  cy.visit(url);
});

Cypress.Commands.add('Graphql', (query, variables = {}) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/v3/graphql`,
    headers: { Authorization: 'Bearer ' + Cypress.env('token') },
    body: {
      query,
      variables,
    },
  }).then((res) => cy.wrap(res));
});

Cypress.Commands.add('getDataIdCy', ({ idAlias, options = {} }: { idAlias: string; options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow> }) => {
  const matches = idAlias.replace(/@/, '').split(' > ');

  const tagName = matches[0];
  const childCombinators: string | string[] = matches.slice(1).join(' > ') ?? '';
  const withChildCombinators = childCombinators.length > 0 ? ` > ${childCombinators}` : '';

  return cy.get(`[data-testid="${tagName}"]${withChildCombinators}`, options);
});

Cypress.Commands.add('waitMainPageIsLoaded', () => {
  cy.get('nav[aria-label="breadcrumb"] a').should('contain.text', 'All Events');
  cy.get('[data-testid="table"] > div > span').should('not.exist');
  cy.get('[data-testid="table"] [data-testid="event-row-name"]').should('have.length.greaterThan', 0).first().should('be.visible').and('not.be.empty');
});

Cypress.Commands.add('waitFilesTabIsLoaded', () => {
  cy.get('nav[aria-label="breadcrumb"] a').should('contain.text', 'All Events');
  cy.get('[data-testid^="file-row-cell-"][data-testid$="-0"]').should('have.length.greaterThan', 0).first().should('be.visible');
});

Cypress.Commands.add('waitForElement', (selector: string, timeout = 10000) => cy.get(selector, { timeout }).should('be.visible'));

Cypress.Commands.add('waitForElementToDisappear', (selector: string, timeout = 10000) => cy.get(selector, { timeout }).should('not.exist'));

Cypress.Commands.add('selectFromDropdown', (selector: string, value: string) => cy.get(selector).select(value));

Cypress.Commands.add('repeat', ({ action, times }: { action: unknown; times: number }) => {
  if (typeof action === 'function') {
    Array.from({ length: times }, () => action());
  }
});

Cypress.Commands.add('awaitNetworkResponseCode', ({ alias, code, repeat = 1 }: { alias: string; code: number; repeat?: number }) => {
  cy.repeat({
    action: cy.wait(`${alias}`).its('response.statusCode').should('eq', code),
    times: repeat,
  });
  // cy.assertNoLoading();
});

Cypress.Commands.add(
  'assertTableColumnSorted',
  (label: string, orderBy: string) => {
    cy.log(`🔍 Starting assertTableColumnSorted for ${label} in ${orderBy} order`);

    // Wait for table to be fully loaded
    cy.waitMainPageIsLoaded();
    cy.get('[data-testid="table"] .table__loading').should('not.exist');

    // Map column names to their test IDs
    const columnMap: { [key: string]: string } = {
      'Event Name': 'column-0',
      'Event Time': 'column-1',
    };

    cy.getDataIdCy({ idAlias: columnMap[label] }).then(($th) => {
      const columnIndex = $th.index();
      cy.log(`📊 Column index for ${label}: ${columnIndex}`);

      cy.get('[data-testid^="event-row-"]').then(($rows) => {
        cy.log(`📋 Found ${$rows.length} total rows`);

        const texts: string[] = [];

        // Only check first 3 rows
        const first3Rows = $rows.slice(0, 3);
        cy.log(`🔢 Checking first 3 rows out of ${$rows.length} total`);

        Cypress._.each(first3Rows, ($row) => {
          const cellText = Cypress.$($row)
            .find(`[data-testid$="-${columnIndex}"] [role="cell"]`)
            .text()
            .trim();
          // Skip empty cells
          if (cellText) {
            texts.push(cellText);
          }
        });

        cy.log(`📝 Extracted texts: ${JSON.stringify(texts)}`);

        let sortedTexts: string[] = [...texts];

        if (label === 'Event Time') {
          cy.log(`📅 Processing Event Time column`);
          const dates: Date[] = texts.map((text) => new Date(text));
          const sortedDates = [...dates].sort(
            (a, b) => a.getTime() - b.getTime()
          );

          if (orderBy === 'z-a') {
            sortedDates.reverse();
          }

          sortedTexts = sortedDates.map((date) => {
            const month = date.getMonth() + 1;
            const day = date.getDate();
            const year = date.getFullYear();
            let hours = date.getHours();
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12;
            return `${month}/${day}/${year}, ${hours}:${minutes} ${ampm}`;
          });
        } else {
          cy.log(`📝 Processing Event Name column`);
          sortedTexts.sort();

          if (orderBy === 'z-a') {
            sortedTexts.reverse();
          }
        }

        cy.log(`✅ Expected sorted order: ${JSON.stringify(sortedTexts)}`);
        cy.log(`🔍 Actual order: ${JSON.stringify(texts)}`);

        expect(texts).to.deep.equal(sortedTexts);
        cy.log(`✅ Column ${label} is correctly sorted in ${orderBy} order`);
      });
    });
  }
);
