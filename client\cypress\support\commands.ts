Cypress.Commands.add('LoginToApp', () =>
  cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v1/admin/login`,
      form: true,
      body: {
        userName: Cypress.env('username'),
        password: Cypress.env('password'),
      },
    })
    .then((userInfo) => {
      console.log('=====', userInfo.body);
      Cypress.env('token', userInfo.body.token);
      Cypress.env('userId', userInfo.body.userId);
      return userInfo;
    })
);

Cypress.Commands.add('LoginLandingPage', () => {
  const url = Cypress.env('visitUrl');
  cy.LoginToApp();
  cy.visit(url);
});

Cypress.Commands.add('Graphql', (query, variables = {}) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/v3/graphql`,
    headers: { Authorization: 'Bearer ' + Cypress.env('token') },
    body: {
      query,
      variables,
    },
  }).then((res) => cy.wrap(res));
});

Cypress.Commands.add('getDataIdCy', ({ idAlias, options = {} }: { idAlias: string; options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow> }) => {
  const matches = idAlias.replace(/@/, '').split(' > ');

  const tagName = matches[0];
  const childCombinators: string | string[] = matches.slice(1).join(' > ') ?? '';
  const withChildCombinators = childCombinators.length > 0 ? ` > ${childCombinators}` : '';

  return cy.get(`[data-testid="${tagName}"]${withChildCombinators}`, options);
});

Cypress.Commands.add('waitMainPageIsLoaded', () => {
  cy.get('nav[aria-label="breadcrumb"] a').should('contain.text', 'All Events');
  cy.get('[data-testid="table"] > div > span').should('not.exist');
  cy.get('[data-testid="table"] [data-testid="event-row-name"]').should('have.length.greaterThan', 0).first().should('be.visible').and('not.be.empty');
});

Cypress.Commands.add('waitFilesTabIsLoaded', () => {
  cy.get('nav[aria-label="breadcrumb"] a').should('contain.text', 'All Events');
  cy.get('[data-testid^="file-row-cell-"][data-testid$="-0"]').should('have.length.greaterThan', 0).first().should('be.visible');
});

Cypress.Commands.add('waitForElement', (selector: string, timeout = 10000) => cy.get(selector, { timeout }).should('be.visible'));

Cypress.Commands.add('waitForElementToDisappear', (selector: string, timeout = 10000) => cy.get(selector, { timeout }).should('not.exist'));

Cypress.Commands.add('selectFromDropdown', (selector: string, value: string) => cy.get(selector).select(value));

Cypress.Commands.add('repeat', ({ action, times }: { action: unknown; times: number }) => {
  if (typeof action === 'function') {
    Array.from({ length: times }, () => action());
  }
});

Cypress.Commands.add('awaitNetworkResponseCode', ({ alias, code, repeat = 1 }: { alias: string; code: number; repeat?: number }) => {
  cy.repeat({
    action: cy.wait(`${alias}`).its('response.statusCode').should('eq', code),
    times: repeat,
  });
  // cy.assertNoLoading();
});

Cypress.Commands.add(
  'assertTableColumnSorted',
  (columnName: string, orderBy: string) => {
    cy.log(`Checking if ${columnName} is sorted in ${orderBy} order`);

    // Map column names to their indices
    const columnMap: { [key: string]: number } = {
      'Event Name': 0,
      'Event Time': 1,
    };

    const columnIndex = columnMap[columnName];
    if (columnIndex === undefined) {
      throw new Error(`Column "${columnName}" not found. Available columns: ${Object.keys(columnMap).join(', ')}`);
    }

    // Wait for table to be loaded and get all event rows
    cy.waitMainPageIsLoaded();

    // Wait for any loading state to complete (sorting triggers a network request)
    cy.get('[data-testid="table"] .table__loading').should('not.exist');

    // Additional wait to ensure data is fully loaded
    cy.wait(500);

    cy.get('[data-testid^="event-row-cell-"][data-testid$="-' + columnIndex + '"]').then(($cells) => {
      const texts: string[] = [];

      // Extract text from each cell
      Cypress._.each($cells, ($cell) => {
        const cellText = Cypress.$($cell)
          .find('[role="cell"]')
          .text()
          .trim();
        texts.push(cellText);
      });

      if (texts.length === 0) {
        cy.log('No data found in table');
        return;
      }

      cy.log(`Found ${texts.length} items in ${columnName} column`);
      cy.log(`First few items: ${texts.slice(0, 3).join(', ')}`);

      // Check if the data is sorted correctly
      let isCorrectlySorted = true;
      let failureMessage = '';

      for (let i = 1; i < texts.length; i++) {
        const current = texts[i];
        const previous = texts[i - 1];
        let comparisonResult = false;

        if (columnName === 'Event Time') {
          // Parse dates more carefully - the format is "M/D/YYYY, h:mm AM/PM"
          const currentDate = new Date(current);
          const previousDate = new Date(previous);

          // Check if dates are valid
          if (isNaN(currentDate.getTime()) || isNaN(previousDate.getTime())) {
            cy.log(`Warning: Invalid date format - current: "${current}", previous: "${previous}"`);
            comparisonResult = true; // Skip invalid dates
          } else {
            if (orderBy === 'ascending') {
              comparisonResult = currentDate >= previousDate;
            } else {
              comparisonResult = currentDate <= previousDate;
            }
          }
        } else {
          // Text comparison for Event Name
          const comparison = current.toLowerCase().localeCompare(previous.toLowerCase());

          if (orderBy === 'ascending') {
            comparisonResult = comparison >= 0;
          } else {
            comparisonResult = comparison <= 0;
          }
        }

        if (!comparisonResult) {
          isCorrectlySorted = false;
          failureMessage = `Sort order violation at position ${i}: "${previous}" should come ${orderBy === 'ascending' ? 'before' : 'after'} "${current}"`;
          break;
        }
      }

      if (!isCorrectlySorted) {
        cy.log(`Sort verification failed: ${failureMessage}`);
        throw new Error(failureMessage);
      } else {
        cy.log(`✓ Column ${columnName} is correctly sorted in ${orderBy} order`);
      }
    });
  }
);
