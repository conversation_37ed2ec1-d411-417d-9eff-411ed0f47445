Cypress.Commands.add('LoginToApp', () =>
  cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v1/admin/login`,
      form: true,
      body: {
        userName: Cypress.env('username'),
        password: Cypress.env('password'),
      },
    })
    .then((userInfo) => {
      console.log('=====', userInfo.body);
      Cypress.env('token', userInfo.body.token);
      Cypress.env('userId', userInfo.body.userId);
      return userInfo;
    })
);

Cypress.Commands.add('LoginLandingPage', () => {
  const url = Cypress.env('visitUrl');
  cy.LoginToApp();
  cy.visit(url);
});

Cypress.Commands.add('Graphql', (query, variables = {}) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/v3/graphql`,
    headers: { Authorization: 'Bearer ' + Cypress.env('token') },
    body: {
      query,
      variables,
    },
  }).then((res) => cy.wrap(res));
});

Cypress.Commands.add('getDataIdCy', ({ idAlias, options = {} }: { idAlias: string; options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow> }) => {
  const matches = idAlias.replace(/@/, '').split(' > ');

  const tagName = matches[0];
  const childCombinators: string | string[] = matches.slice(1).join(' > ') ?? '';
  const withChildCombinators = childCombinators.length > 0 ? ` > ${childCombinators}` : '';

  return cy.get(`[data-testid="${tagName}"]${withChildCombinators}`, options);
});

Cypress.Commands.add('waitMainPageIsLoaded', () => {
  cy.get('nav[aria-label="breadcrumb"] a').should('contain.text', 'All Events');
  cy.get('[data-testid="table"] > div > span').should('not.exist');
  cy.get('[data-testid="table"] [data-testid="event-row-name"]').should('have.length.greaterThan', 0).first().should('be.visible').and('not.be.empty');
});

Cypress.Commands.add('waitFilesTabIsLoaded', () => {
  cy.get('nav[aria-label="breadcrumb"] a').should('contain.text', 'All Events');
  cy.get('[data-testid^="file-row-cell-"][data-testid$="-0"]').should('have.length.greaterThan', 0).first().should('be.visible');
});

Cypress.Commands.add('waitForElement', (selector: string, timeout = 10000) => cy.get(selector, { timeout }).should('be.visible'));

Cypress.Commands.add('waitForElementToDisappear', (selector: string, timeout = 10000) => cy.get(selector, { timeout }).should('not.exist'));

Cypress.Commands.add('selectFromDropdown', (selector: string, value: string) => cy.get(selector).select(value));

Cypress.Commands.add('repeat', ({ action, times }: { action: unknown; times: number }) => {
  if (typeof action === 'function') {
    Array.from({ length: times }, () => action());
  }
});

Cypress.Commands.add('awaitNetworkResponseCode', ({ alias, code, repeat = 1 }: { alias: string; code: number; repeat?: number }) => {
  cy.repeat({
    action: cy.wait(`${alias}`).its('response.statusCode').should('eq', code),
    times: repeat,
  });
  // cy.assertNoLoading();
});

Cypress.Commands.add(
  'assertTableColumnSorted',
  (columnName: string, sortedBy: string) => {
    cy.log(`Checking if ${columnName} is sorted ${sortedBy}`);

    // Map column names to their indices
    const columnMap: { [key: string]: number } = {
      'Event Name': 0,
      'Event Time': 1,
    };

    const columnIndex = columnMap[columnName];
    if (columnIndex === undefined) {
      throw new Error(`Column "${columnName}" not found`);
    }

    // Wait for table to be loaded
    cy.waitMainPageIsLoaded();
    cy.get('[data-testid="table"] .table__loading').should('not.exist');

    // Simple check: just verify first 3 rows are in correct order
    cy.get('[data-testid^="event-row-cell-"][data-testid$="-' + columnIndex + '"]')
      .should('have.length.greaterThan', 2)
      .then(($cells) => {
        const first3 = Array.from($cells.slice(0, 3)).map(cell =>
          Cypress.$(cell).find('[role="cell"]').text().trim()
        );

        cy.log(`First 3 items: ${first3.join(', ')}`);

        // Simple comparison for first 3 items
        if (sortedBy === 'a-z') {
          expect(first3[0].toLowerCase() <= first3[1].toLowerCase(), `${first3[0]} should come before ${first3[1]}`).to.be.true;
          expect(first3[1].toLowerCase() <= first3[2].toLowerCase(), `${first3[1]} should come before ${first3[2]}`).to.be.true;
        } else {
          expect(first3[0].toLowerCase() >= first3[1].toLowerCase(), `${first3[0]} should come after ${first3[1]}`).to.be.true;
          expect(first3[1].toLowerCase() >= first3[2].toLowerCase(), `${first3[1]} should come after ${first3[2]}`).to.be.true;
        }
      });
  }
);
